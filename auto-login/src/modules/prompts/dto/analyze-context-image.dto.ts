import { IsString, <PERSON>NotEmpty, IsOptional } from 'class-validator';

export class AnalyzeContextImageDto {
  @IsNotEmpty()
  @IsString()
  image_data: string; // Base64 encoded image data

  @IsNotEmpty()
  @IsString()
  mime_type: string; // Image MIME type (e.g., 'image/jpeg', 'image/png')

  @IsOptional()
  @IsString()
  model?: string; // OpenRouter model to use for analysis
}

export interface ContextAnalysisResult {
  location: string;
  time: string;
  weather: string;
  lightingDetails: string;
}
