import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  UseGuards,
  Req,
} from '@nestjs/common';
import { PromptsService } from './prompts.service';
import { CreatePromptDto } from './dto/create-prompt.dto';
import { CreatePromptCategoryDto } from './dto/create-prompt-category.dto';
import { QueryPromptsDto } from './dto/query-prompts.dto';
import { QueryPromptHistoriesDto } from './dto/query-prompt-histories.dto';
import { GeneratePromptDto } from './dto/generate-prompt.dto';
import { AnalyzeContextImageDto } from './dto/analyze-context-image.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TranslationService } from './services/translation.service';

@Controller()
@UseGuards(JwtAuthGuard)
export class PromptsController {
  constructor(
    private readonly promptsService: PromptsService,
    private readonly translationService: TranslationService,
  ) {}

  // GET /prompt-categories - Get danh sách categories
  @Get('prompt-categories')
  async getPromptCategories(@Query('lang') lang?: string) {
    try {
      return await this.promptsService.findAllCategories(lang);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompt categories: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompts/by-category?page=1&pageSize=12&category_id=16&search_text= - Get prompt list by category id
  @Get('prompts/by-category')
  async getPromptsByCategory(@Query() queryDto: QueryPromptsDto) {
    try {
      return await this.promptsService.findPromptsByCategory(queryDto);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompts by category: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompts/by-category-optimized - Optimized version for better translation performance
  @Get('prompts/by-category-optimized')
  async getPromptsByCategoryOptimized(@Query() queryDto: QueryPromptsDto) {
    try {
      return await this.promptsService.findPromptsByCategoryOptimized(queryDto);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompts by category: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompts/:id - Get prompt detail
  @Get('prompts/:id')
  async getPromptById(
    @Param('id', ParseIntPipe) id: number,
    @Query('lang') lang?: string,
  ) {
    try {
      return await this.promptsService.findPromptById(id, lang);
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        'Failed to retrieve prompt: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Admin routes (protected)
  @Post('admin/prompt-categories')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async createPromptCategory(
    @Body() createPromptCategoryDto: CreatePromptCategoryDto,
  ) {
    try {
      return await this.promptsService.createCategory(createPromptCategoryDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create prompt category: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('admin/prompts')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async createPrompt(@Body() createPromptDto: CreatePromptDto) {
    try {
      return await this.promptsService.createPrompt(createPromptDto);
    } catch (error) {
      throw new HttpException(
        'Failed to create prompt: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('admin/prompts')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getAllPrompts() {
    try {
      return await this.promptsService.findAllPrompts();
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve all prompts: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // POST /prompts/generate - Generate prompt result using OpenRouter
  @Post('prompts/generate')
  async generatePromptResult(
    @Body() generateDto: GeneratePromptDto,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.promptsService.generatePromptResult(
        generateDto,
        userId,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to generate prompt result: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('topics')
  async getTopics() {
    try {
      return await this.promptsService.findAllTopics();
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve topics: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompt-histories - Get user's prompt histories
  @Get('prompt-histories')
  async getPromptHistories(
    @Query() queryDto: QueryPromptHistoriesDto,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.promptsService.findPromptHistoriesByUser(
        userId,
        queryDto,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompt histories: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // GET /prompt-histories/:id - Get specific prompt history
  @Get('prompt-histories/:id')
  async getPromptHistoryById(
    @Param('id', ParseIntPipe) id: number,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.promptsService.findPromptHistoryById(id, userId);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve prompt history: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // POST /prompts/analyze-context-image - Analyze context image and return structured data
  @Post('prompts/analyze-context-image')
  @UseGuards(JwtAuthGuard)
  async analyzeContextImage(
    @Body() analyzeDto: AnalyzeContextImageDto,
  ) {
    try {
      return await this.promptsService.analyzeContextImage(analyzeDto);
    } catch (error) {
      throw new HttpException(
        'Failed to analyze context image: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
